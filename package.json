{"name": "eneco-chat", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-router-dom": "^7.6.3", "sass": "^1.89.2", "zod": "^3.25.67", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.30.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}