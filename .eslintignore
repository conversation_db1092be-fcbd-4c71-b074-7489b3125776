# Dependencies
node_modules/

# Build outputs
dist/
build/

# Configuration files
*.config.js
*.config.ts
vite.config.ts
eslint.config.js

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage reports
coverage/
.nyc_output/

# Temporary files
.tmp/
temp/
