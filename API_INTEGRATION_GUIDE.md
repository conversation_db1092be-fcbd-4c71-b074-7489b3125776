# 🔗 API Integration Guide

## Step-by-Step Setup for Your Real API Endpoint

### 1. **Configure Environment Variables**

Update your `.env.local` file with your actual API details:

```env
# Your API Configuration
VITE_API_BASE_URL=https://your-api-domain.com/api
# For local development:
# VITE_API_BASE_URL=http://localhost:8000/api

# Disable mock API
VITE_USE_MOCK_API=false

# Authentication (if needed)
VITE_API_KEY=your-api-key-here
VITE_AUTH_TOKEN=your-auth-token-here
```

### 2. **Customize API Endpoints**

Edit `src/services/chatService.ts` to match your API structure:

```typescript
// Current endpoints (customize these):
const ENDPOINTS = {
  SEND_MESSAGE: '/chat/messages',           // POST - Send a new message
  GET_MESSAGES: '/chat/messages',           // GET - Retrieve messages
  CREATE_CONVERSATION: '/chat/conversations', // POST - Create conversation
  GET_CONVERSATIONS: '/chat/conversations',  // GET - List conversations
  DELETE_CONVERSATION: (id: string) => `/chat/conversations/${id}`, // DELETE
  HEALTH: '/health',                        // GET - Health check
} as const;
```

### 3. **Expected API Response Formats**

Your API should return data in these formats:

#### Send Message Response:
```json
{
  "data": {
    "id": "msg_123",
    "content": "Hello world",
    "role": "user",
    "timestamp": "2024-01-01T12:00:00Z",
    "status": "sent",
    "conversationId": "conv_456"
  },
  "success": true,
  "message": "Message sent successfully",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### Get Messages Response:
```json
{
  "data": {
    "messages": [
      {
        "id": "msg_123",
        "content": "Hello",
        "role": "user",
        "timestamp": "2024-01-01T12:00:00Z",
        "status": "sent",
        "conversationId": "conv_456"
      }
    ],
    "hasMore": false,
    "total": 1
  },
  "success": true,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### Create Conversation Response:
```json
{
  "data": {
    "id": "conv_456",
    "title": "New Chat",
    "createdAt": "2024-01-01T12:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z",
    "messageCount": 0
  },
  "success": true,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 4. **Authentication Setup**

If your API requires authentication, update `src/utils/api.ts`:

```typescript
// Add auth headers in the request interceptor
client.interceptors.request.use((config) => {
  // Add API key
  if (import.meta.env.VITE_API_KEY) {
    config.headers['X-API-Key'] = import.meta.env.VITE_API_KEY;
  }
  
  // Add auth token
  if (import.meta.env.VITE_AUTH_TOKEN) {
    config.headers['Authorization'] = `Bearer ${import.meta.env.VITE_AUTH_TOKEN}`;
  }
  
  return config;
});
```

### 5. **CORS Configuration**

Ensure your API server allows requests from your frontend domain:

```javascript
// Example Express.js CORS setup
app.use(cors({
  origin: ['http://localhost:5174', 'https://your-frontend-domain.com'],
  credentials: true
}));
```

### 6. **Testing Your Integration**

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Check the browser console** for API requests
3. **Monitor network tab** in DevTools
4. **Test message sending** and conversation creation

### 7. **Common Issues & Solutions**

#### CORS Errors:
- Configure your API server to allow frontend domain
- Add proper CORS headers

#### Authentication Errors:
- Verify API keys and tokens
- Check header format requirements

#### Endpoint Not Found:
- Verify endpoint URLs match your API
- Check HTTP methods (GET, POST, DELETE)

#### Response Format Errors:
- Ensure your API returns data in expected format
- Check TypeScript interfaces match your API

### 8. **Next Steps**

1. Replace placeholder URLs with your actual endpoints
2. Add authentication if required
3. Test all functionality
4. Deploy and configure production environment variables

---

**Need Help?** Check the browser console and network tab for detailed error messages.
