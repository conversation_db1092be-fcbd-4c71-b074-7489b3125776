// Import design tokens (order matters - mixins first)
@import './tokens/mixins';
@import './tokens/colors';
@import './tokens/typography';
@import './tokens/spacing';

// Import component styles
@import './components/button';
@import './components/message-bubble';
@import './components/message-list';
@import './components/message-input';
@import './components/chat-container';
@import './components/connection-status';
@import './components/typing-indicator';

// Import Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

// Reset and base styles
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
  font-size: 16px;
}

body {
  height: 100%;
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.2s ease, color 0.2s ease;
}

#root {
  height: 100%;
}

// Scrollbar styling
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}

// Focus styles
:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

// Button reset
button {
  border: none;
  background: none;
  font: inherit;
  cursor: pointer;
  color: inherit;
}

// Input reset
input,
textarea {
  border: none;
  background: none;
  font: inherit;
  color: inherit;
}

input:focus,
textarea:focus {
  outline: none;
}

// Link reset
a {
  color: inherit;
  text-decoration: none;
}

// List reset
ul,
ol {
  list-style: none;
}

// Image reset
img {
  max-width: 100%;
  height: auto;
}

// Utility classes
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

// Animation utilities
@mixin fade-in {
  animation: fadeIn 0.2s ease-in-out;
}

@mixin slide-up {
  animation: slideUp 0.3s ease-out;
}

@mixin scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

// Theme transition
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

// Export breakpoints for JavaScript
:export {
  breakpointSm: #{$breakpoint-sm};
  breakpointMd: #{$breakpoint-md};
  breakpointLg: #{$breakpoint-lg};
  breakpointXl: #{$breakpoint-xl};
}
