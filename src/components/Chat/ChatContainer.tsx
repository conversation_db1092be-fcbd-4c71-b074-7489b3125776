import React, { useEffect } from 'react';
import type { ChatContainerProps, ViewMode} from '../../types/chat';
import { VIEW_MODE } from '../../types/chat';
import { useChatStore, useThemeStore } from '../../store';
import { useSendMessage } from '../../hooks/api';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import ConnectionStatus from './ConnectionStatus';
import { Minimize2, Maximize2, X, Settings } from 'lucide-react';
import clsx from 'clsx';

const ChatContainer: React.FC<ChatContainerProps> = ({
  viewMode: propViewMode,
  onViewModeChange,
  onMinimize,
  onClose,
  className,
  children,
}) => {
  const {
    messages,
    isTyping,
    viewMode: storeViewMode,
    isMinimized,
    unreadCount,
    addMessage,
    updateMessage,
    setViewMode,
    setMinimized,
    markAsRead,
  } = useChatStore();
  
  const { toggleTheme } = useThemeStore();
  const sendMessageMutation = useSendMessage();
  
  const currentViewMode = propViewMode || storeViewMode;
  
  // Mark messages as read when chat is opened
  useEffect(() => {
    if (!isMinimized && unreadCount > 0) {
      markAsRead();
    }
  }, [isMinimized, unreadCount, markAsRead]);
  
  const handleSendMessage = async (content: string) => {
    // Add user message optimistically
    const userMessage = {
      content,
      sender: 'user' as const,
      status: 'sending' as const,
    };
    
    addMessage(userMessage);
    
    try {
      // Send message via API
      await sendMessageMutation.mutateAsync({
        message: content,
        conversationId: 'default', // TODO: Use actual conversation ID
      });
    } catch (error) {
      console.error('Failed to send message:', error);
      // The mutation will handle updating the message status to error
    }
  };
  
  const handleRetryMessage = async (messageId: string) => {
    const message = messages.find(m => m.id === messageId);
    if (!message) return;
    
    // Update message status to sending
    updateMessage(messageId, { status: 'sending' });
    
    try {
      await sendMessageMutation.mutateAsync({
        message: message.content,
        conversationId: 'default',
      });
    } catch (error) {
      console.error('Failed to retry message:', error);
    }
  };
  
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
    onViewModeChange?.(mode);
  };
  
  const handleMinimize = () => {
    setMinimized(true);
    onMinimize?.();
  };
  
  const handleMaximize = () => {
    if (currentViewMode === VIEW_MODE.FLOATING) {
      handleViewModeChange(VIEW_MODE.FULLSCREEN);
    } else {
      handleViewModeChange(VIEW_MODE.FLOATING);
    }
  };
  
  const handleClose = () => {
    onClose?.();
  };
  
  const isFloating = currentViewMode === VIEW_MODE.FLOATING;
  const isFullscreen = currentViewMode === VIEW_MODE.FULLSCREEN;
  const isFullpage = currentViewMode === VIEW_MODE.FULLPAGE;
  
  return (
    <div
      className={clsx(
        'chat-container',
        {
          'chat-container--floating': isFloating,
          'chat-container--fullscreen': isFullscreen,
          'chat-container--fullpage': isFullpage,
          'chat-container--minimized': isMinimized,
        },
        className
      )}
    >
      {/* Header */}
      <div className="chat-container__header">
        <div className="chat-container__header-content">
          <div className="chat-container__title">
            <h2>Eneco Assistant</h2>
            <ConnectionStatus />
          </div>
          
          <div className="chat-container__header-actions">
            <button
              onClick={toggleTheme}
              className="chat-container__action"
              aria-label="Toggle theme"
            >
              <Settings size={18} />
            </button>
            
            {isFloating && (
              <button
                onClick={handleMaximize}
                className="chat-container__action"
                aria-label="Maximize chat"
              >
                <Maximize2 size={18} />
              </button>
            )}
            
            {(isFullscreen || isFullpage) && (
              <button
                onClick={handleMaximize}
                className="chat-container__action"
                aria-label="Minimize to floating"
              >
                <Minimize2 size={18} />
              </button>
            )}
            
            {isFloating && (
              <button
                onClick={handleMinimize}
                className="chat-container__action"
                aria-label="Minimize chat"
              >
                <Minimize2 size={18} />
              </button>
            )}
            
            {onClose && (
              <button
                onClick={handleClose}
                className="chat-container__action chat-container__action--close"
                aria-label="Close chat"
              >
                <X size={18} />
              </button>
            )}
          </div>
        </div>
        
        {unreadCount > 0 && isMinimized && (
          <div className="chat-container__unread-badge">
            {unreadCount}
          </div>
        )}
      </div>
      
      {/* Main Content */}
      {!isMinimized && (
        <div className="chat-container__content">
          <div className="chat-container__messages">
            <MessageList
              messages={messages}
              isTyping={isTyping}
              onRetryMessage={handleRetryMessage}
            />
          </div>
          
          <div className="chat-container__input">
            <MessageInput
              onSendMessage={handleSendMessage}
              disabled={sendMessageMutation.isPending}
              placeholder="Ask me anything about Eneco..."
            />
          </div>
        </div>
      )}
      
      {children}
    </div>
  );
};

export default ChatContainer;
