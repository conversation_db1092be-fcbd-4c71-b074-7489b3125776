import React, { useEffect, useRef, useState } from 'react';
import { ChatMessage } from '../../types';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import { useChatStore } from '../../store';
import clsx from 'clsx';

interface MessageListProps {
  messages?: ChatMessage[];
  isTyping?: boolean;
  onRetryMessage?: (messageId: string) => void;
  autoScroll?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const MessageList: React.FC<MessageListProps> = ({
  messages: propMessages,
  isTyping: propIsTyping,
  onRetryMessage,
  autoScroll = true,
  className,
  children,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  
  // Use store values if props not provided
  const storeMessages = useChatStore((state) => state.messages);
  const storeIsTyping = useChatStore((state) => state.isTyping);
  
  const messages = propMessages || storeMessages;
  const isTyping = propIsTyping !== undefined ? propIsTyping : storeIsTyping;
  
  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = (smooth = true) => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto',
        block: 'end',
      });
    }
  };
  
  // Handle scroll events to detect user scrolling
  const handleScroll = () => {
    if (!containerRef.current) return;
    
    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px threshold
    
    setIsUserScrolling(!isAtBottom);
    setShowScrollToBottom(!isAtBottom && messages.length > 0);
  };
  
  // Auto-scroll effect
  useEffect(() => {
    if (autoScroll && !isUserScrolling) {
      scrollToBottom();
    }
  }, [messages.length, isTyping, autoScroll, isUserScrolling]);
  
  // Reset user scrolling when reaching bottom
  useEffect(() => {
    if (!containerRef.current) return;
    
    const container = containerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 10;
    
    if (isAtBottom) {
      setIsUserScrolling(false);
      setShowScrollToBottom(false);
    }
  }, []);
  
  // Intersection Observer for auto-scroll detection
  useEffect(() => {
    if (!messagesEndRef.current) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsUserScrolling(false);
          setShowScrollToBottom(false);
        }
      },
      { threshold: 0.1 }
    );
    
    observer.observe(messagesEndRef.current);
    
    return () => observer.disconnect();
  }, []);
  
  const handleScrollToBottom = () => {
    setIsUserScrolling(false);
    scrollToBottom();
  };
  
  const groupedMessages = React.useMemo(() => {
    const groups: Array<{
      date: string;
      messages: ChatMessage[];
    }> = [];
    
    messages.forEach((message) => {
      const messageDate = message.timestamp.toDateString();
      const lastGroup = groups[groups.length - 1];
      
      if (lastGroup && lastGroup.date === messageDate) {
        lastGroup.messages.push(message);
      } else {
        groups.push({
          date: messageDate,
          messages: [message],
        });
      }
    });
    
    return groups;
  }, [messages]);
  
  const formatDateHeader = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString(undefined, {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    }
  };
  
  if (messages.length === 0 && !isTyping) {
    return (
      <div className={clsx('message-list message-list--empty', className)}>
        <div className="message-list__empty-state">
          <div className="message-list__empty-icon">💬</div>
          <h3 className="message-list__empty-title">Start a conversation</h3>
          <p className="message-list__empty-description">
            Send a message to begin chatting with our AI assistant.
          </p>
        </div>
        {children}
      </div>
    );
  }
  
  return (
    <div className={clsx('message-list', className)}>
      <div
        ref={containerRef}
        className="message-list__container"
        onScroll={handleScroll}
      >
        <div className="message-list__content">
          {groupedMessages.map((group) => (
            <div key={group.date} className="message-list__group">
              <div className="message-list__date-header">
                {formatDateHeader(group.date)}
              </div>
              
              <div className="message-list__messages">
                {group.messages.map((message, index) => {
                  const prevMessage = group.messages[index - 1];
                  const isConsecutive = 
                    prevMessage && 
                    prevMessage.sender === message.sender &&
                    message.timestamp.getTime() - prevMessage.timestamp.getTime() < 60000; // 1 minute
                  
                  return (
                    <MessageBubble
                      key={message.id}
                      message={message}
                      isOwn={message.sender === 'user'}
                      showTimestamp={!isConsecutive}
                      onRetry={onRetryMessage}
                      className={clsx({
                        'message-bubble--consecutive': isConsecutive,
                      })}
                    />
                  );
                })}
              </div>
            </div>
          ))}
          
          {isTyping && (
            <TypingIndicator className="message-list__typing" />
          )}
          
          <div ref={messagesEndRef} className="message-list__end" />
        </div>
      </div>
      
      {showScrollToBottom && (
        <button
          onClick={handleScrollToBottom}
          className="message-list__scroll-to-bottom"
          aria-label="Scroll to bottom"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
            <path d="M7 14l5 5 5-5z" />
          </svg>
        </button>
      )}
      
      {children}
    </div>
  );
};

export default MessageList;
