import React from 'react';
import clsx from 'clsx';

interface TypingIndicatorProps {
  className?: string;
  children?: React.ReactNode;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  className,
  children,
}) => {
  return (
    <div className={clsx('typing-indicator', className)}>
      <div className="typing-indicator__content">
        <div className="typing-indicator__avatar">
          <div className="typing-indicator__avatar-icon">🤖</div>
        </div>
        <div className="typing-indicator__bubble">
          <div className="typing-indicator__dots">
            <span className="typing-indicator__dot"></span>
            <span className="typing-indicator__dot"></span>
            <span className="typing-indicator__dot"></span>
          </div>
          <div className="typing-indicator__text">AI is typing...</div>
        </div>
      </div>
      {children}
    </div>
  );
};

export default TypingIndicator;
