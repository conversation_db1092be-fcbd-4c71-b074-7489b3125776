import React from 'react';
import { MessageBubbleProps } from '../../types/chat';
import { formatDistanceToNow } from 'date-fns';
import { RefreshCw, AlertCircle } from 'lucide-react';
import clsx from 'clsx';

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwn,
  showTimestamp = true,
  onRetry,
  className,
  children,
}) => {
  const handleRetry = () => {
    if (onRetry && message.status === 'error') {
      onRetry(message.id);
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    try {
      return formatDistanceToNow(timestamp, { addSuffix: true });
    } catch {
      return timestamp.toLocaleTimeString();
    }
  };

  return (
    <div
      className={clsx(
        'message-bubble',
        {
          'message-bubble--own': isOwn,
          'message-bubble--other': !isOwn,
          'message-bubble--sending': message.status === 'sending',
          'message-bubble--error': message.status === 'error',
        },
        className
      )}
    >
      <div className="message-bubble__content">
        <div className="message-bubble__text">
          {message.content}
        </div>
        
        {message.status === 'error' && message.error && (
          <div className="message-bubble__error">
            <AlertCircle size={14} />
            <span>{message.error}</span>
            {onRetry && (
              <button
                onClick={handleRetry}
                className="message-bubble__retry"
                aria-label="Retry sending message"
              >
                <RefreshCw size={14} />
              </button>
            )}
          </div>
        )}
        
        {showTimestamp && (
          <div className="message-bubble__timestamp">
            {formatTimestamp(message.timestamp)}
            {message.status === 'sending' && (
              <span className="message-bubble__status">Sending...</span>
            )}
          </div>
        )}
      </div>
      
      {children}
    </div>
  );
};

export default MessageBubble;
