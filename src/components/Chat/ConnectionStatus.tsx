import React from 'react';
import { ConnectionStatusProps, CONNECTION_STATUS } from '../../types';
import { useConnectionStore } from '../../store';
import { Wifi, WifiOff, RefreshCw } from 'lucide-react';
import clsx from 'clsx';

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  status: propStatus,
  onRetry,
  showDetails = false,
  className,
  children,
}) => {
  const {
    status: storeStatus,
    isConnected,
    isConnecting,
    hasError,
    canRetry,
  } = useConnectionStore();
  
  const status = propStatus || storeStatus;
  const connectionState = {
    isConnected: isConnected(),
    isConnecting: isConnecting(),
    hasError: hasError(),
    canRetry: canRetry(),
  };
  
  const getStatusIcon = () => {
    if (connectionState.isConnecting) {
      return <RefreshCw size={14} className="connection-status__icon--spinning" />;
    } else if (connectionState.isConnected) {
      return <Wifi size={14} />;
    } else {
      return <WifiOff size={14} />;
    }
  };
  
  const getStatusText = () => {
    switch (status.status) {
      case CONNECTION_STATUS.CONNECTED:
        return 'Online';
      case CONNECTION_STATUS.CONNECTING:
        return 'Connecting...';
      case CONNECTION_STATUS.DISCONNECTED:
        return 'Offline';
      case CONNECTION_STATUS.ERROR:
        return 'Connection Error';
      default:
        return 'Unknown';
    }
  };
  
  const getStatusColor = () => {
    switch (status.status) {
      case CONNECTION_STATUS.CONNECTED:
        return 'success';
      case CONNECTION_STATUS.CONNECTING:
        return 'warning';
      case CONNECTION_STATUS.DISCONNECTED:
        return 'muted';
      case CONNECTION_STATUS.ERROR:
        return 'error';
      default:
        return 'muted';
    }
  };
  
  const handleRetry = () => {
    if (onRetry && connectionState.canRetry) {
      onRetry();
    }
  };
  
  return (
    <div
      className={clsx(
        'connection-status',
        `connection-status--${getStatusColor()}`,
        {
          'connection-status--detailed': showDetails,
          'connection-status--retryable': connectionState.canRetry && onRetry,
        },
        className
      )}
    >
      <div className="connection-status__indicator">
        {getStatusIcon()}
        <span className="connection-status__text">
          {getStatusText()}
        </span>
      </div>
      
      {showDetails && status.error && (
        <div className="connection-status__error">
          {status.error}
        </div>
      )}
      
      {connectionState.canRetry && onRetry && (
        <button
          onClick={handleRetry}
          className="connection-status__retry"
          aria-label="Retry connection"
        >
          <RefreshCw size={12} />
        </button>
      )}
      
      {children}
    </div>
  );
};

export default ConnectionStatus;
