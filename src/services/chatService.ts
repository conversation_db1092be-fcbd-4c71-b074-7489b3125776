import {
  type ChatService,
  type SendMessagePayload,
  type SendMessageApiResponse,
  type GetMessagesParams,
  type GetMessagesApiResponse,
  type CreateConversationPayload,
  type ConversationApiResponse,
  type GetConversationsParams,
  type GetConversationsApiResponse,
  type HealthCheckResponse,
} from '../types/api';
import { apiClientWithRetry } from '../utils/api';

// Chat API endpoints
const ENDPOINTS = {
  SEND_MESSAGE: '/chat/messages',
  GET_MESSAGES: '/chat/messages',
  CREATE_CONVERSATION: '/chat/conversations',
  GET_CONVERSATIONS: '/chat/conversations',
  DELETE_CONVERSATION: (id: string) => `/chat/conversations/${id}`,
  HEALTH: '/health',
} as const;

// Implementation of ChatService
class ChatServiceImpl implements ChatService {
  async sendMessage(payload: SendMessagePayload): Promise<SendMessageApiResponse> {
    const response = await apiClientWithRetry.post<SendMessageApiResponse>(
      ENDPOINTS.SEND_MESSAGE,
      payload,
      {},
      {
        retries: 2, // Fewer retries for message sending
        retryDelay: 500,
      }
    );
    
    return response.data;
  }

  async getMessages(params: GetMessagesParams): Promise<GetMessagesApiResponse> {
    const response = await apiClientWithRetry.get<GetMessagesApiResponse>(
      ENDPOINTS.GET_MESSAGES,
      {
        params,
      }
    );
    
    return response.data;
  }

  async createConversation(payload: CreateConversationPayload): Promise<ConversationApiResponse> {
    const response = await apiClientWithRetry.post<ConversationApiResponse>(
      ENDPOINTS.CREATE_CONVERSATION,
      payload
    );
    
    return response.data;
  }

  async getConversations(params: GetConversationsParams): Promise<GetConversationsApiResponse> {
    const response = await apiClientWithRetry.get<GetConversationsApiResponse>(
      ENDPOINTS.GET_CONVERSATIONS,
      {
        params,
      }
    );
    
    return response.data;
  }

  async deleteConversation(conversationId: string): Promise<void> {
    await apiClientWithRetry.delete(
      ENDPOINTS.DELETE_CONVERSATION(conversationId)
    );
  }

  async healthCheck(): Promise<HealthCheckResponse> {
    const response = await apiClientWithRetry.get<HealthCheckResponse>(
      ENDPOINTS.HEALTH,
      {},
      {
        retries: 1,
        retryDelay: 1000,
      }
    );
    
    return response.data;
  }
}

// Export singleton instance
export const chatService = new ChatServiceImpl();

// Mock service for development/testing
class MockChatService implements ChatService {
  private conversations: ConversationApiResponse[] = [];
  private messages: Record<string, ChatMessage[]> = {};
  
  async sendMessage(payload: SendMessagePayload): Promise<SendMessageApiResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
    
    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const conversationId = payload.conversationId || 'default_conversation';
    
    // Simulate AI response
    const responses = [
      "Hello! How can I help you today?",
      "That's an interesting question. Let me think about that...",
      "I understand what you're asking. Here's what I think:",
      "Thanks for sharing that with me. I'd be happy to help.",
      "That's a great point. Let me provide some more information:",
      "I see what you mean. Here's my perspective on that:",
    ];
    
    const response = responses[Math.floor(Math.random() * responses.length)];
    
    return {
      messageId,
      response,
      conversationId,
      timestamp: new Date().toISOString(),
      metadata: {
        processingTime: Math.random() * 2000,
        confidence: 0.8 + Math.random() * 0.2,
      },
    };
  }

  async getMessages(params: GetMessagesParams): Promise<GetMessagesApiResponse> {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const conversationMessages = this.messages[params.conversationId] || [];
    const limit = params.limit || 50;
    const offset = params.offset || 0;
    
    const messages = conversationMessages.slice(offset, offset + limit);
    
    return {
      messages,
      hasMore: offset + limit < conversationMessages.length,
      total: conversationMessages.length,
      conversationId: params.conversationId,
    };
  }

  async createConversation(payload: CreateConversationPayload): Promise<ConversationApiResponse> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const conversation: ConversationApiResponse = {
      id: `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: payload.title || 'New Conversation',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      messageCount: 0,
      metadata: payload.metadata,
    };
    
    this.conversations.push(conversation);
    this.messages[conversation.id] = [];
    
    return conversation;
  }

  async getConversations(params: GetConversationsParams): Promise<GetConversationsApiResponse> {
    await new Promise(resolve => setTimeout(resolve, 200));
    
    const limit = params.limit || 20;
    const offset = params.offset || 0;
    
    const conversations = [...this.conversations];
    
    // Sort conversations
    if (params.sortBy) {
      conversations.sort((a, b) => {
        const sortBy = params.sortBy;
        if (!sortBy) return 0;

        const aValue = a[sortBy as keyof ConversationApiResponse];
        const bValue = b[sortBy as keyof ConversationApiResponse];

        if (params.sortOrder === 'desc') {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });
    }
    
    const paginatedConversations = conversations.slice(offset, offset + limit);
    
    return {
      conversations: paginatedConversations,
      hasMore: offset + limit < conversations.length,
      total: conversations.length,
    };
  }

  async deleteConversation(conversationId: string): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, 200));

    this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
    // Remove messages for this conversation
    if (conversationId in this.messages) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { [conversationId]: _removed, ...remainingMessages } = this.messages;
      this.messages = remainingMessages;
    }
  }

  async healthCheck(): Promise<HealthCheckResponse> {
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      uptime: Date.now(),
      services: {
        database: 'up',
        ai: 'up',
        cache: 'up',
      },
    };
  }
}

// Export mock service for development
export const mockChatService = new MockChatService();

// Service factory
export const createChatService = (useMock = false): ChatService => {
  if (useMock || import.meta.env.DEV) {
    return mockChatService;
  }
  return chatService;
};

// Default export based on environment
export default createChatService(import.meta.env.VITE_USE_MOCK_API === 'true');
