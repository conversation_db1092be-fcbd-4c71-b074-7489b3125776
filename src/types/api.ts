// API related types and interfaces

import { ChatMessage } from './chat';

// Base API Response
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

// Error Response
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

// Chat API Endpoints
export interface ChatApiEndpoints {
  sendMessage: string;
  getMessages: string;
  getConversations: string;
  createConversation: string;
  deleteConversation: string;
  healthCheck: string;
}

// Send Message API
export interface SendMessagePayload {
  message: string;
  conversationId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface SendMessageApiResponse {
  messageId: string;
  response: string;
  conversationId: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

// Get Messages API
export interface GetMessagesParams {
  conversationId: string;
  limit?: number;
  offset?: number;
  before?: string;
  after?: string;
}

export interface GetMessagesApiResponse {
  messages: ChatMessage[];
  hasMore: boolean;
  total: number;
  conversationId: string;
}

// Conversation API
export interface CreateConversationPayload {
  title?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface ConversationApiResponse {
  id: string;
  title?: string;
  createdAt: string;
  updatedAt: string;
  messageCount: number;
  metadata?: Record<string, any>;
}

export interface GetConversationsParams {
  userId?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'messageCount';
  sortOrder?: 'asc' | 'desc';
}

export interface GetConversationsApiResponse {
  conversations: ConversationApiResponse[];
  hasMore: boolean;
  total: number;
}

// Health Check API
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  services: {
    database: 'up' | 'down';
    ai: 'up' | 'down';
    cache: 'up' | 'down';
  };
}

// WebSocket Message Types
export type WebSocketMessageType = 
  | 'message'
  | 'typing_start'
  | 'typing_stop'
  | 'connection_ack'
  | 'error'
  | 'ping'
  | 'pong';

export interface WebSocketMessage {
  type: WebSocketMessageType;
  payload: any;
  id?: string;
  timestamp: string;
}

export interface WebSocketTypingMessage {
  type: 'typing_start' | 'typing_stop';
  payload: {
    conversationId: string;
    userId: string;
  };
  timestamp: string;
}

export interface WebSocketChatMessage {
  type: 'message';
  payload: {
    message: ChatMessage;
    conversationId: string;
  };
  timestamp: string;
}

export interface WebSocketErrorMessage {
  type: 'error';
  payload: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

// HTTP Client Configuration
export interface HttpClientConfig {
  baseURL: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  headers: Record<string, string>;
}

// Request Configuration
export interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
}

// Response Interceptor
export interface ResponseInterceptor<T = any> {
  onFulfilled?: (response: ApiResponse<T>) => ApiResponse<T> | Promise<ApiResponse<T>>;
  onRejected?: (error: ApiError) => any;
}

// Request Interceptor
export interface RequestInterceptor {
  onFulfilled?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>;
  onRejected?: (error: any) => any;
}

// Retry Configuration
export interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: ApiError) => boolean;
  onRetry?: (retryCount: number, error: ApiError) => void;
}

// Cache Configuration
export interface CacheConfig {
  enabled: boolean;
  ttl: number; // Time to live in milliseconds
  maxSize: number;
  keyGenerator?: (config: RequestConfig) => string;
}

// API Client Interface
export interface ApiClient {
  get<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
  patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<ApiResponse<T>>;
}

// Chat Service Interface
export interface ChatService {
  sendMessage(payload: SendMessagePayload): Promise<SendMessageApiResponse>;
  getMessages(params: GetMessagesParams): Promise<GetMessagesApiResponse>;
  createConversation(payload: CreateConversationPayload): Promise<ConversationApiResponse>;
  getConversations(params: GetConversationsParams): Promise<GetConversationsApiResponse>;
  deleteConversation(conversationId: string): Promise<void>;
  healthCheck(): Promise<HealthCheckResponse>;
}

// WebSocket Service Interface
export interface WebSocketService {
  connect(url: string): Promise<void>;
  disconnect(): void;
  send(message: WebSocketMessage): void;
  subscribe(type: WebSocketMessageType, handler: (message: WebSocketMessage) => void): () => void;
  isConnected(): boolean;
  getReadyState(): number;
}

// Query Keys for TanStack Query
export const QUERY_KEYS = {
  MESSAGES: (conversationId: string) => ['messages', conversationId] as const,
  CONVERSATIONS: (userId?: string) => ['conversations', userId] as const,
  HEALTH: () => ['health'] as const,
} as const;

// Mutation Keys for TanStack Query
export const MUTATION_KEYS = {
  SEND_MESSAGE: 'sendMessage',
  CREATE_CONVERSATION: 'createConversation',
  DELETE_CONVERSATION: 'deleteConversation',
} as const;

// Default API Configuration
export const DEFAULT_API_CONFIG: HttpClientConfig = {
  baseURL: '/api',
  timeout: 10000,
  retries: 3,
  retryDelay: 1000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Error Codes
export const API_ERROR_CODES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMITED: 'RATE_LIMITED',
  SERVER_ERROR: 'SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
} as const;

export type ApiErrorCode = typeof API_ERROR_CODES[keyof typeof API_ERROR_CODES];
