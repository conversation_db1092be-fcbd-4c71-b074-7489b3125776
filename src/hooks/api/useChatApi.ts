import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  SendMessagePayload,
  GetMessagesParams,
  CreateConversationPayload,
  GetConversationsParams,
  QUERY_KEYS,
  MUTATION_KEYS,
} from '../../types/api';
import chatService from '../../services/chatService';
import { optimisticUpdates } from './queryClient';
import { useChatStore } from '../../store';

// Hook for sending messages
export const useSendMessage = () => {
  const queryClient = useQueryClient();
  const { addMessage, updateMessage } = useChatStore();
  
  return useMutation({
    mutationKey: [MUTATION_KEYS.SEND_MESSAGE],
    mutationFn: async (payload: SendMessagePayload) => {
      return await chatService.sendMessage(payload);
    },
    onMutate: async (payload) => {
      // Create optimistic user message
      const tempMessage = {
        id: `temp_${Date.now()}`,
        content: payload.message,
        sender: 'user' as const,
        status: 'sending' as const,
        timestamp: new Date(),
      };
      
      // Add to store optimistically
      addMessage(tempMessage);
      
      // Cancel any outgoing refetches
      const conversationId = payload.conversationId || 'default';
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.MESSAGES(conversationId) });
      
      // Snapshot the previous value
      const previousMessages = queryClient.getQueryData(QUERY_KEYS.MESSAGES(conversationId));
      
      // Optimistically update the cache
      const rollback = optimisticUpdates.addMessage(conversationId, tempMessage);
      
      return { previousMessages, rollback, tempMessage };
    },
    onSuccess: (data, payload, context) => {
      // Update the temporary message to sent status
      if (context?.tempMessage) {
        updateMessage(context.tempMessage.id, {
          status: 'sent',
        });
      }
      
      // Add the AI response
      addMessage({
        content: data.response,
        sender: 'agent',
        status: 'sent',
      });
      
      // Invalidate and refetch messages
      const conversationId = payload.conversationId || 'default';
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.MESSAGES(conversationId) });
    },
    onError: (error, payload, context) => {
      // Update the temporary message to error status
      if (context?.tempMessage) {
        updateMessage(context.tempMessage.id, {
          status: 'error',
          error: error.message,
        });
      }
      
      // Rollback optimistic update
      context?.rollback?.();
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['messages'] });
    },
  });
};

// Hook for fetching messages
export const useMessages = (params: GetMessagesParams, enabled = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.MESSAGES(params.conversationId),
    queryFn: () => chatService.getMessages(params),
    enabled: enabled && !!params.conversationId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
  });
};

// Hook for creating conversations
export const useCreateConversation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: [MUTATION_KEYS.CREATE_CONVERSATION],
    mutationFn: (payload: CreateConversationPayload) => {
      return chatService.createConversation(payload);
    },
    onSuccess: () => {
      // Invalidate conversations list
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
    },
  });
};

// Hook for fetching conversations
export const useConversations = (params: GetConversationsParams = {}, enabled = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.CONVERSATIONS(params.userId),
    queryFn: () => chatService.getConversations(params),
    enabled,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook for deleting conversations
export const useDeleteConversation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: [MUTATION_KEYS.DELETE_CONVERSATION],
    mutationFn: (conversationId: string) => {
      return chatService.deleteConversation(conversationId);
    },
    onMutate: async (conversationId) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['conversations'] });
      
      // Snapshot the previous value
      const previousConversations = queryClient.getQueryData(['conversations']);
      
      // Optimistically remove the conversation
      queryClient.setQueryData(['conversations'], (old: any) => {
        if (!old) return old;
        return {
          ...old,
          conversations: old.conversations.filter((conv: any) => conv.id !== conversationId),
          total: old.total - 1,
        };
      });
      
      return { previousConversations };
    },
    onError: (error, conversationId, context) => {
      // Rollback on error
      if (context?.previousConversations) {
        queryClient.setQueryData(['conversations'], context.previousConversations);
      }
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
    },
  });
};

// Hook for health check
export const useHealthCheck = (enabled = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.HEALTH(),
    queryFn: () => chatService.healthCheck(),
    enabled,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Check every minute
    retry: 1, // Only retry once for health checks
  });
};

// Hook for infinite messages (pagination)
export const useInfiniteMessages = (conversationId: string, enabled = true) => {
  // For now, return a simple query instead of infinite query
  // TODO: Implement proper infinite query when needed
  return useQuery({
    queryKey: [...QUERY_KEYS.MESSAGES(conversationId), 'infinite'],
    queryFn: async () => {
      return chatService.getMessages({
        conversationId,
        limit: 50,
        offset: 0,
      });
    },
    enabled: enabled && !!conversationId,
    staleTime: 30 * 1000,
  });
};

// Hook for message search
export const useMessageSearch = (query: string, conversationId?: string, enabled = true) => {
  return useQuery({
    queryKey: ['messages', 'search', query, conversationId],
    queryFn: async () => {
      // This would be implemented when search API is available
      const params: GetMessagesParams = {
        conversationId: conversationId || 'all',
        limit: 50,
      };
      
      const result = await chatService.getMessages(params);
      
      // Client-side filtering for now
      const filteredMessages = result.messages.filter(message =>
        message.content.toLowerCase().includes(query.toLowerCase())
      );
      
      return {
        ...result,
        messages: filteredMessages,
        total: filteredMessages.length,
      };
    },
    enabled: enabled && query.length > 2, // Only search with 3+ characters
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for conversation statistics
export const useConversationStats = (conversationId: string, enabled = true) => {
  return useQuery({
    queryKey: ['conversations', conversationId, 'stats'],
    queryFn: async () => {
      const messages = await chatService.getMessages({
        conversationId,
        limit: 1000, // Get all messages for stats
      });
      
      const userMessages = messages.messages.filter(m => m.sender === 'user');
      const agentMessages = messages.messages.filter(m => m.sender === 'agent');
      
      return {
        totalMessages: messages.total,
        userMessages: userMessages.length,
        agentMessages: agentMessages.length,
        averageResponseTime: 0, // Would be calculated from actual data
        lastActivity: messages.messages[messages.messages.length - 1]?.timestamp,
      };
    },
    enabled: enabled && !!conversationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Utility hooks for common operations
export const useChatOperations = () => {
  const sendMessage = useSendMessage();
  const createConversation = useCreateConversation();
  const deleteConversation = useDeleteConversation();
  
  return {
    sendMessage: sendMessage.mutate,
    sendMessageAsync: sendMessage.mutateAsync,
    createConversation: createConversation.mutate,
    createConversationAsync: createConversation.mutateAsync,
    deleteConversation: deleteConversation.mutate,
    deleteConversationAsync: deleteConversation.mutateAsync,
    
    // Loading states
    isSendingMessage: sendMessage.isPending,
    isCreatingConversation: createConversation.isPending,
    isDeletingConversation: deleteConversation.isPending,
    
    // Error states
    sendMessageError: sendMessage.error,
    createConversationError: createConversation.error,
    deleteConversationError: deleteConversation.error,
  };
};
