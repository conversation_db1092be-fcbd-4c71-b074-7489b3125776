// Export all API hooks and utilities

// Chat API hooks
export {
  useSendMessage,
  useMessages,
  useCreateConversation,
  useConversations,
  useDeleteConversation,
  useHealthCheck,
  useInfiniteMessages,
  useMessageSearch,
  useConversationStats,
  useChatOperations,
} from './useChatApi';

// Query client utilities
export {
  queryClient,
  createQueryClient,
  devQueryClient,
  prodQueryClient,
  getQueryClient,
  setupQueryClientEvents,
  invalidateQueries,
  prefetchQueries,
  queryCacheUtils,
  optimisticUpdates,
} from './queryClient';

// Re-export TanStack Query hooks for convenience
export {
  useQuery,
  useMutation,
  useQueryClient,
  useInfiniteQuery,
  useSuspenseQuery,
  useQueries,
  useMutationState,
  useIsFetching,
  useIsMutating,
} from '@tanstack/react-query';
