import type { DefaultOptions } from '@tanstack/react-query';
import { QueryClient } from '@tanstack/react-query';
import { type ApiError } from '../../types/api';
import { type ChatMessage, type MessageStatus } from '../../types/chat';

// Types for optimistic updates
interface MessagesQueryData {
  messages: ChatMessage[];
  hasMore: boolean;
  total: number;
}

interface MessageUpdates {
  content?: string;
  status?: MessageStatus;
  timestamp?: Date;
  error?: string;
}

// Default query options
const defaultQueryOptions: DefaultOptions = {
  queries: {
    // Stale time: 5 minutes
    staleTime: 5 * 60 * 1000,
    // Cache time: 10 minutes
    gcTime: 10 * 60 * 1000,
    // Retry configuration
    retry: (failureCount, error) => {
      // Check if error has the structure of an ApiError
      const apiError = error as unknown as ApiError;

      // Don't retry on client errors (4xx) if it's an API error
      if (apiError && typeof apiError === 'object' && 'code' in apiError) {
        if (apiError.code === 'UNAUTHORIZED' ||
            apiError.code === 'FORBIDDEN' ||
            apiError.code === 'NOT_FOUND' ||
          apiError.code === 'VALIDATION_ERROR') {
          return false;
        }
      }

      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
    // Retry delay with exponential backoff
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // Refetch on window focus in production
    refetchOnWindowFocus: process.env.NODE_ENV === 'production',
    // Refetch on reconnect
    refetchOnReconnect: true,
    // Background refetch interval: 5 minutes
    refetchInterval: 5 * 60 * 1000,
  },
  mutations: {
    // Retry mutations once
    retry: 1,
    // Retry delay for mutations
    retryDelay: 1000,
  },
};

// Create query client
export const queryClient = new QueryClient({
  defaultOptions: defaultQueryOptions,
});

// Query client configuration for different environments
export const createQueryClient = (options?: Partial<DefaultOptions>) => {
  const mergedOptions = {
    queries: {
      ...defaultQueryOptions.queries,
      ...options?.queries,
    },
    mutations: {
      ...defaultQueryOptions.mutations,
      ...options?.mutations,
    },
  };
  
  return new QueryClient({
    defaultOptions: mergedOptions,
  });
};

// Development query client with more aggressive refetching
export const devQueryClient = createQueryClient({
  queries: {
    staleTime: 0, // Always consider data stale in development
    gcTime: 5 * 60 * 1000, // Shorter cache time
    refetchOnWindowFocus: true,
    refetchInterval: false, // Disable background refetch in development
  },
});

// Production query client with conservative settings
export const prodQueryClient = createQueryClient({
  queries: {
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
    refetchInterval: 10 * 60 * 1000, // 10 minutes
  },
});

// Get appropriate query client based on environment
export const getQueryClient = () => {
  if (process.env.NODE_ENV === 'development') {
    return devQueryClient;
  }
  return prodQueryClient;
};

// Query client event handlers
export const setupQueryClientEvents = () => {
  // Setup basic error handling
  if (process.env.NODE_ENV === 'development') {
    // Development logging can be added here if needed
    console.log('Query client initialized in development mode');
  }
};

// Query invalidation utilities
export const invalidateQueries = {
  // Invalidate all chat-related queries
  chat: () => {
    queryClient.invalidateQueries({ queryKey: ['chat'] });
  },
  
  // Invalidate messages for a specific conversation
  messages: (conversationId: string) => {
    queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
  },
  
  // Invalidate conversations
  conversations: () => {
    queryClient.invalidateQueries({ queryKey: ['conversations'] });
  },
  
  // Invalidate health check
  health: () => {
    queryClient.invalidateQueries({ queryKey: ['health'] });
  },
  
  // Invalidate all queries
  all: () => {
    queryClient.invalidateQueries();
  },
};

// Query prefetching utilities
export const prefetchQueries = {
  // Prefetch conversations
  conversations: async (userId?: string) => {
    await queryClient.prefetchQuery({
      queryKey: ['conversations', userId],
      queryFn: () => {
        // This would be implemented in the actual hook
        return Promise.resolve([]);
      },
    });
  },
  
  // Prefetch messages for a conversation
  messages: async (conversationId: string) => {
    await queryClient.prefetchQuery({
      queryKey: ['messages', conversationId],
      queryFn: () => {
        // This would be implemented in the actual hook
        return Promise.resolve([]);
      },
    });
  },
};

// Query cache utilities
export const queryCacheUtils = {
  // Get cached data
  getCachedData: <T>(queryKey: unknown[]) => {
    return queryClient.getQueryData<T>(queryKey);
  },
  
  // Set cached data
  setCachedData: <T>(queryKey: unknown[], data: T) => {
    queryClient.setQueryData(queryKey, data);
  },
  
  // Remove cached data
  removeCachedData: (queryKey: unknown[]) => {
    queryClient.removeQueries({ queryKey });
  },
  
  // Get query state
  getQueryState: (queryKey: unknown[]) => {
    return queryClient.getQueryState(queryKey);
  },
  
  // Cancel queries
  cancelQueries: (queryKey: unknown[]) => {
    return queryClient.cancelQueries({ queryKey });
  },
};

// Optimistic update utilities
export const optimisticUpdates = {
  // Add message optimistically
  addMessage: (conversationId: string, message: ChatMessage) => {
    const queryKey = ['messages', conversationId];
    const previousData = queryClient.getQueryData<MessagesQueryData>(queryKey);

    // Optimistically update the cache
    queryClient.setQueryData<MessagesQueryData>(queryKey, (old) => {
      if (!old) return { messages: [message], hasMore: false, total: 1 };
      return {
        ...old,
        messages: [...old.messages, message],
        total: old.total + 1,
      };
    });

    // Return rollback function
    return () => {
      queryClient.setQueryData(queryKey, previousData);
    };
  },

  // Update message optimistically
  updateMessage: (conversationId: string, messageId: string, updates: MessageUpdates) => {
    const queryKey = ['messages', conversationId];
    const previousData = queryClient.getQueryData<MessagesQueryData>(queryKey);

    queryClient.setQueryData<MessagesQueryData>(queryKey, (old) => {
      if (!old) return old;
      return {
        ...old,
        messages: old.messages.map((msg) =>
          msg.id === messageId ? { ...msg, ...updates } : msg
        ),
      };
    });

    return () => {
      queryClient.setQueryData(queryKey, previousData);
    };
  },
};
