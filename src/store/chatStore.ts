import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { 
  ChatMessage, 
  ConnectionStatus, 
  ViewMode, 
  MessageStatus,
  CONNECTION_STATUS,
  VIEW_MODE 
} from '../types';

// Chat Store Interface
interface ChatStore {
  // State
  messages: ChatMessage[];
  isTyping: boolean;
  connectionStatus: ConnectionStatus;
  viewMode: ViewMode;
  isMinimized: boolean;
  unreadCount: number;
  currentConversationId: string | null;
  
  // Actions
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
  updateMessage: (id: string, updates: Partial<ChatMessage>) => void;
  removeMessage: (id: string) => void;
  clearMessages: () => void;
  setTyping: (isTyping: boolean) => void;
  setConnectionStatus: (status: ConnectionStatus) => void;
  setViewMode: (mode: ViewMode) => void;
  setMinimized: (minimized: boolean) => void;
  markAsRead: () => void;
  incrementUnreadCount: () => void;
  setCurrentConversationId: (id: string | null) => void;
  
  // Computed
  getMessageById: (id: string) => ChatMessage | undefined;
  getLastMessage: () => ChatMessage | undefined;
  getUnsentMessages: () => ChatMessage[];
  isConnected: () => boolean;
}

// Generate unique ID for messages
const generateMessageId = (): string => {
  return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Initial connection status
const initialConnectionStatus: ConnectionStatus = {
  status: CONNECTION_STATUS.DISCONNECTED,
  lastConnected: undefined,
  error: undefined,
  retryCount: 0,
  isRetrying: false,
};

// Create the chat store
export const useChatStore = create<ChatStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        messages: [],
        isTyping: false,
        connectionStatus: initialConnectionStatus,
        viewMode: VIEW_MODE.FLOATING,
        isMinimized: false,
        unreadCount: 0,
        currentConversationId: null,

        // Actions
        addMessage: (messageData) => {
          const message: ChatMessage = {
            ...messageData,
            id: generateMessageId(),
            timestamp: new Date(),
          };

          set((state) => ({
            messages: [...state.messages, message],
            // Increment unread count if it's an agent message and chat is minimized
            unreadCount: 
              message.sender === 'agent' && state.isMinimized 
                ? state.unreadCount + 1 
                : state.unreadCount,
          }));
        },

        updateMessage: (id, updates) => {
          set((state) => ({
            messages: state.messages.map((msg) =>
              msg.id === id ? { ...msg, ...updates } : msg
            ),
          }));
        },

        removeMessage: (id) => {
          set((state) => ({
            messages: state.messages.filter((msg) => msg.id !== id),
          }));
        },

        clearMessages: () => {
          set({ messages: [], unreadCount: 0 });
        },

        setTyping: (isTyping) => {
          set({ isTyping });
        },

        setConnectionStatus: (connectionStatus) => {
          set({ connectionStatus });
        },

        setViewMode: (viewMode) => {
          set({ viewMode });
        },

        setMinimized: (isMinimized) => {
          set((state) => ({
            isMinimized,
            // Clear unread count when opening chat
            unreadCount: isMinimized ? state.unreadCount : 0,
          }));
        },

        markAsRead: () => {
          set({ unreadCount: 0 });
        },

        incrementUnreadCount: () => {
          set((state) => ({
            unreadCount: state.unreadCount + 1,
          }));
        },

        setCurrentConversationId: (currentConversationId) => {
          set({ currentConversationId });
        },

        // Computed getters
        getMessageById: (id) => {
          return get().messages.find((msg) => msg.id === id);
        },

        getLastMessage: () => {
          const messages = get().messages;
          return messages.length > 0 ? messages[messages.length - 1] : undefined;
        },

        getUnsentMessages: () => {
          return get().messages.filter((msg) => msg.status === 'sending' || msg.status === 'error');
        },

        isConnected: () => {
          return get().connectionStatus.status === CONNECTION_STATUS.CONNECTED;
        },
      }),
      {
        name: 'chat-store',
        // Only persist certain fields
        partialize: (state) => ({
          messages: state.messages,
          viewMode: state.viewMode,
          currentConversationId: state.currentConversationId,
        }),
        // Custom storage to handle Date objects
        storage: {
          getItem: (name) => {
            const str = localStorage.getItem(name);
            if (!str) return null;
            
            try {
              const parsed = JSON.parse(str);
              // Convert timestamp strings back to Date objects
              if (parsed.state?.messages) {
                parsed.state.messages = parsed.state.messages.map((msg: any) => ({
                  ...msg,
                  timestamp: new Date(msg.timestamp),
                }));
              }
              return parsed;
            } catch {
              return null;
            }
          },
          setItem: (name, value) => {
            localStorage.setItem(name, JSON.stringify(value));
          },
          removeItem: (name) => {
            localStorage.removeItem(name);
          },
        },
      }
    ),
    {
      name: 'chat-store',
    }
  )
);

// Selectors for better performance
export const useChatMessages = () => useChatStore((state) => state.messages);
export const useChatTyping = () => useChatStore((state) => state.isTyping);
export const useChatConnection = () => useChatStore((state) => state.connectionStatus);
export const useChatViewMode = () => useChatStore((state) => state.viewMode);
export const useChatMinimized = () => useChatStore((state) => state.isMinimized);
export const useChatUnreadCount = () => useChatStore((state) => state.unreadCount);
export const useChatActions = () => useChatStore((state) => ({
  addMessage: state.addMessage,
  updateMessage: state.updateMessage,
  removeMessage: state.removeMessage,
  clearMessages: state.clearMessages,
  setTyping: state.setTyping,
  setConnectionStatus: state.setConnectionStatus,
  setViewMode: state.setViewMode,
  setMinimized: state.setMinimized,
  markAsRead: state.markAsRead,
  incrementUnreadCount: state.incrementUnreadCount,
  setCurrentConversationId: state.setCurrentConversationId,
}));

// Helper functions
export const createUserMessage = (content: string): Omit<ChatMessage, 'id' | 'timestamp'> => ({
  content,
  sender: 'user',
  status: 'sending',
});

export const createAgentMessage = (content: string): Omit<ChatMessage, 'id' | 'timestamp'> => ({
  content,
  sender: 'agent',
  status: 'sent',
});

export const createErrorMessage = (content: string, error: string): Omit<ChatMessage, 'id' | 'timestamp'> => ({
  content,
  sender: 'user',
  status: 'error',
  error,
});
