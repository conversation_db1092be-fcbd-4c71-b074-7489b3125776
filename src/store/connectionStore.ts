import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ConnectionStatus, CONNECTION_STATUS } from '../types/chat';

// Connection Store Interface
interface ConnectionStore {
  // State
  status: ConnectionStatus;
  lastPingTime: Date | null;
  reconnectAttempts: number;
  maxReconnectAttempts: number;
  reconnectDelay: number;
  isManualDisconnect: boolean;
  
  // Actions
  setStatus: (status: ConnectionStatus['status']) => void;
  setError: (error: string) => void;
  clearError: () => void;
  incrementRetryCount: () => void;
  resetRetryCount: () => void;
  setRetrying: (isRetrying: boolean) => void;
  setLastConnected: (date: Date) => void;
  setLastPingTime: (date: Date) => void;
  setManualDisconnect: (manual: boolean) => void;
  incrementReconnectAttempts: () => void;
  resetReconnectAttempts: () => void;
  
  // Computed
  isConnected: () => boolean;
  isConnecting: () => boolean;
  isDisconnected: () => boolean;
  hasError: () => boolean;
  canRetry: () => boolean;
  shouldReconnect: () => boolean;
  getNextReconnectDelay: () => number;
}

// Initial connection status
const initialStatus: ConnectionStatus = {
  status: CONNECTION_STATUS.DISCONNECTED,
  lastConnected: undefined,
  error: undefined,
  retryCount: 0,
  isRetrying: false,
};

// Create the connection store
export const useConnectionStore = create<ConnectionStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      status: initialStatus,
      lastPingTime: null,
      reconnectAttempts: 0,
      maxReconnectAttempts: 5,
      reconnectDelay: 1000, // Start with 1 second
      isManualDisconnect: false,

      // Actions
      setStatus: (status) => {
        set((state) => ({
          status: {
            ...state.status,
            status,
          },
        }));
      },

      setError: (error) => {
        set((state) => ({
          status: {
            ...state.status,
            status: CONNECTION_STATUS.ERROR,
            error,
          },
        }));
      },

      clearError: () => {
        set((state) => ({
          status: {
            ...state.status,
            error: undefined,
          },
        }));
      },

      incrementRetryCount: () => {
        set((state) => ({
          status: {
            ...state.status,
            retryCount: (state.status.retryCount || 0) + 1,
          },
        }));
      },

      resetRetryCount: () => {
        set((state) => ({
          status: {
            ...state.status,
            retryCount: 0,
          },
        }));
      },

      setRetrying: (isRetrying) => {
        set((state) => ({
          status: {
            ...state.status,
            isRetrying,
          },
        }));
      },

      setLastConnected: (lastConnected) => {
        set((state) => ({
          status: {
            ...state.status,
            lastConnected,
          },
        }));
      },

      setLastPingTime: (lastPingTime) => {
        set({ lastPingTime });
      },

      setManualDisconnect: (isManualDisconnect) => {
        set({ isManualDisconnect });
      },

      incrementReconnectAttempts: () => {
        set((state) => ({
          reconnectAttempts: state.reconnectAttempts + 1,
          // Exponential backoff with jitter
          reconnectDelay: Math.min(
            state.reconnectDelay * 2 + Math.random() * 1000,
            30000 // Max 30 seconds
          ),
        }));
      },

      resetReconnectAttempts: () => {
        set({
          reconnectAttempts: 0,
          reconnectDelay: 1000, // Reset to 1 second
        });
      },

      // Computed getters
      isConnected: () => {
        return get().status.status === CONNECTION_STATUS.CONNECTED;
      },

      isConnecting: () => {
        return get().status.status === CONNECTION_STATUS.CONNECTING;
      },

      isDisconnected: () => {
        const status = get().status.status;
        return status === CONNECTION_STATUS.DISCONNECTED || status === CONNECTION_STATUS.ERROR;
      },

      hasError: () => {
        return get().status.status === CONNECTION_STATUS.ERROR;
      },

      canRetry: () => {
        const state = get();
        return (
          state.isDisconnected() &&
          !state.status.isRetrying &&
          !state.isManualDisconnect &&
          state.reconnectAttempts < state.maxReconnectAttempts
        );
      },

      shouldReconnect: () => {
        const state = get();
        return (
          !state.isManualDisconnect &&
          state.isDisconnected() &&
          state.reconnectAttempts < state.maxReconnectAttempts
        );
      },

      getNextReconnectDelay: () => {
        return get().reconnectDelay;
      },
    }),
    {
      name: 'connection-store',
    }
  )
);

// Selectors for better performance
export const useConnectionStatus = () => useConnectionStore((state) => state.status);
export const useConnectionState = () => useConnectionStore((state) => ({
  isConnected: state.isConnected(),
  isConnecting: state.isConnecting(),
  isDisconnected: state.isDisconnected(),
  hasError: state.hasError(),
  canRetry: state.canRetry(),
  shouldReconnect: state.shouldReconnect(),
}));

export const useConnectionActions = () => useConnectionStore((state) => ({
  setStatus: state.setStatus,
  setError: state.setError,
  clearError: state.clearError,
  incrementRetryCount: state.incrementRetryCount,
  resetRetryCount: state.resetRetryCount,
  setRetrying: state.setRetrying,
  setLastConnected: state.setLastConnected,
  setLastPingTime: state.setLastPingTime,
  setManualDisconnect: state.setManualDisconnect,
  incrementReconnectAttempts: state.incrementReconnectAttempts,
  resetReconnectAttempts: state.resetReconnectAttempts,
}));

// Helper functions
export const getConnectionStatusColor = (status: ConnectionStatus['status']): string => {
  switch (status) {
    case CONNECTION_STATUS.CONNECTED:
      return 'var(--color-status-connected)';
    case CONNECTION_STATUS.CONNECTING:
      return 'var(--color-status-connecting)';
    case CONNECTION_STATUS.DISCONNECTED:
      return 'var(--color-status-disconnected)';
    case CONNECTION_STATUS.ERROR:
      return 'var(--color-status-error)';
    default:
      return 'var(--color-status-disconnected)';
  }
};

export const getConnectionStatusLabel = (status: ConnectionStatus['status']): string => {
  switch (status) {
    case CONNECTION_STATUS.CONNECTED:
      return 'Connected';
    case CONNECTION_STATUS.CONNECTING:
      return 'Connecting...';
    case CONNECTION_STATUS.DISCONNECTED:
      return 'Disconnected';
    case CONNECTION_STATUS.ERROR:
      return 'Connection Error';
    default:
      return 'Unknown';
  }
};

export const getConnectionStatusIcon = (status: ConnectionStatus['status']): string => {
  switch (status) {
    case CONNECTION_STATUS.CONNECTED:
      return '🟢';
    case CONNECTION_STATUS.CONNECTING:
      return '🟡';
    case CONNECTION_STATUS.DISCONNECTED:
      return '⚪';
    case CONNECTION_STATUS.ERROR:
      return '🔴';
    default:
      return '⚪';
  }
};

// Connection health check
export const isConnectionHealthy = (status: ConnectionStatus, lastPingTime: Date | null): boolean => {
  if (status.status !== CONNECTION_STATUS.CONNECTED) {
    return false;
  }
  
  if (!lastPingTime) {
    return true; // No ping data yet, assume healthy
  }
  
  const now = new Date();
  const timeSinceLastPing = now.getTime() - lastPingTime.getTime();
  const maxPingInterval = 30000; // 30 seconds
  
  return timeSinceLastPing < maxPingInterval;
};

// Format connection uptime
export const formatConnectionUptime = (lastConnected: Date | undefined): string => {
  if (!lastConnected) {
    return 'Never connected';
  }
  
  const now = new Date();
  const uptime = now.getTime() - lastConnected.getTime();
  
  const seconds = Math.floor(uptime / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days}d ${hours % 24}h`;
  } else if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};
