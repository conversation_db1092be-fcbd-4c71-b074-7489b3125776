(()=>{var $;function U(C){return U=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},U(C)}function A(C,G){var H=Object.keys(C);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(C);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(C,X).enumerable})),H.push.apply(H,J)}return H}function Q(C){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?A(Object(H),!0).forEach(function(J){E(C,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(H)):A(Object(H)).forEach(function(J){Object.defineProperty(C,J,Object.getOwnPropertyDescriptor(H,J))})}return C}function E(C,G,H){if(G=N(G),G in C)Object.defineProperty(C,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else C[G]=H;return C}function N(C){var G=z(C,"string");return U(G)=="symbol"?G:String(G)}function z(C,G){if(U(C)!="object"||!C)return C;var H=C[Symbol.toPrimitive];if(H!==void 0){var J=H.call(C,G||"default");if(U(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(C)}var W=Object.defineProperty,JC=function C(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},D={lessThanXSeconds:{one:"mindre \xE4n en sekund",other:"mindre \xE4n {{count}} sekunder"},xSeconds:{one:"en sekund",other:"{{count}} sekunder"},halfAMinute:"en halv minut",lessThanXMinutes:{one:"mindre \xE4n en minut",other:"mindre \xE4n {{count}} minuter"},xMinutes:{one:"en minut",other:"{{count}} minuter"},aboutXHours:{one:"ungef\xE4r en timme",other:"ungef\xE4r {{count}} timmar"},xHours:{one:"en timme",other:"{{count}} timmar"},xDays:{one:"en dag",other:"{{count}} dagar"},aboutXWeeks:{one:"ungef\xE4r en vecka",other:"ungef\xE4r {{count}} veckor"},xWeeks:{one:"en vecka",other:"{{count}} veckor"},aboutXMonths:{one:"ungef\xE4r en m\xE5nad",other:"ungef\xE4r {{count}} m\xE5nader"},xMonths:{one:"en m\xE5nad",other:"{{count}} m\xE5nader"},aboutXYears:{one:"ungef\xE4r ett \xE5r",other:"ungef\xE4r {{count}} \xE5r"},xYears:{one:"ett \xE5r",other:"{{count}} \xE5r"},overXYears:{one:"\xF6ver ett \xE5r",other:"\xF6ver {{count}} \xE5r"},almostXYears:{one:"n\xE4stan ett \xE5r",other:"n\xE4stan {{count}} \xE5r"}},S=["noll","en","tv\xE5","tre","fyra","fem","sex","sju","\xE5tta","nio","tio","elva","tolv"],M=function C(G,H,J){var X,Y=D[G];if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else X=Y.other.replace("{{count}}",H<13?S[H]:String(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)return"om "+X;else return X+" sedan";return X};function K(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):C.defaultWidth,J=C.formats[H]||C.formats[C.defaultWidth];return J}}var R={full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"y-MM-dd"},L={full:"'kl'. HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},V={full:"{{date}} 'kl.' {{time}}",long:"{{date}} 'kl.' {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},j={date:K({formats:R,defaultWidth:"full"}),time:K({formats:L,defaultWidth:"full"}),dateTime:K({formats:V,defaultWidth:"full"})},w={lastWeek:"'i' EEEE's kl.' p",yesterday:"'ig\xE5r kl.' p",today:"'idag kl.' p",tomorrow:"'imorgon kl.' p",nextWeek:"EEEE 'kl.' p",other:"P"},_=function C(G,H,J,X){return w[G]};function I(C){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=C.formattingValues[Z]||C.formattingValues[Y]}else{var B=C.defaultWidth,q=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;X=C.values[q]||C.values[B]}var T=C.argumentCallback?C.argumentCallback(G):G;return X[T]}}var f={narrow:["f.Kr.","e.Kr."],abbreviated:["f.Kr.","e.Kr."],wide:["f\xF6re Kristus","efter Kristus"]},F={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1:a kvartalet","2:a kvartalet","3:e kvartalet","4:e kvartalet"]},P={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["jan.","feb.","mars","apr.","maj","juni","juli","aug.","sep.","okt.","nov.","dec."],wide:["januari","februari","mars","april","maj","juni","juli","augusti","september","oktober","november","december"]},v={narrow:["S","M","T","O","T","F","L"],short:["s\xF6","m\xE5","ti","on","to","fr","l\xF6"],abbreviated:["s\xF6n","m\xE5n","tis","ons","tors","fre","l\xF6r"],wide:["s\xF6ndag","m\xE5ndag","tisdag","onsdag","torsdag","fredag","l\xF6rdag"]},k={narrow:{am:"fm",pm:"em",midnight:"midnatt",noon:"middag",morning:"morg.",afternoon:"efterm.",evening:"kv\xE4ll",night:"natt"},abbreviated:{am:"f.m.",pm:"e.m.",midnight:"midnatt",noon:"middag",morning:"morgon",afternoon:"efterm.",evening:"kv\xE4ll",night:"natt"},wide:{am:"f\xF6rmiddag",pm:"eftermiddag",midnight:"midnatt",noon:"middag",morning:"morgon",afternoon:"eftermiddag",evening:"kv\xE4ll",night:"natt"}},h={narrow:{am:"fm",pm:"em",midnight:"midnatt",noon:"middag",morning:"p\xE5 morg.",afternoon:"p\xE5 efterm.",evening:"p\xE5 kv\xE4llen",night:"p\xE5 natten"},abbreviated:{am:"fm",pm:"em",midnight:"midnatt",noon:"middag",morning:"p\xE5 morg.",afternoon:"p\xE5 efterm.",evening:"p\xE5 kv\xE4llen",night:"p\xE5 natten"},wide:{am:"fm",pm:"em",midnight:"midnatt",noon:"middag",morning:"p\xE5 morgonen",afternoon:"p\xE5 eftermiddagen",evening:"p\xE5 kv\xE4llen",night:"p\xE5 natten"}},b=function C(G,H){var J=Number(G),X=J%100;if(X>20||X<10)switch(X%10){case 1:case 2:return J+":a"}return J+":e"},m={ordinalNumber:b,era:I({values:f,defaultWidth:"wide"}),quarter:I({values:F,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:I({values:P,defaultWidth:"wide"}),day:I({values:v,defaultWidth:"wide"}),dayPeriod:I({values:k,defaultWidth:"wide",formattingValues:h,defaultFormattingWidth:"wide"})};function O(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&C.matchPatterns[J]||C.matchPatterns[C.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],B=J&&C.parsePatterns[J]||C.parsePatterns[C.defaultParseWidth],q=Array.isArray(B)?c(B,function(x){return x.test(Z)}):y(B,function(x){return x.test(Z)}),T;T=C.valueCallback?C.valueCallback(q):q,T=H.valueCallback?H.valueCallback(T):T;var HC=G.slice(Z.length);return{value:T,rest:HC}}}function y(C,G){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&G(C[H]))return H;return}function c(C,G){for(var H=0;H<C.length;H++)if(G(C[H]))return H;return}function d(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(C.matchPattern);if(!J)return null;var X=J[0],Y=G.match(C.parsePattern);if(!Y)return null;var Z=C.valueCallback?C.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var B=G.slice(X.length);return{value:Z,rest:B}}}var g=/^(\d+)(:a|:e)?/i,p=/\d+/i,u={narrow:/^(f\.? ?Kr\.?|f\.? ?v\.? ?t\.?|e\.? ?Kr\.?|v\.? ?t\.?)/i,abbreviated:/^(f\.? ?Kr\.?|f\.? ?v\.? ?t\.?|e\.? ?Kr\.?|v\.? ?t\.?)/i,wide:/^(före Kristus|före vår tid|efter Kristus|vår tid)/i},l={any:[/^f/i,/^[ev]/i]},i={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](:a|:e)? kvartalet/i},n={any:[/1/i,/2/i,/3/i,/4/i]},s={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar[s]?|apr|maj|jun[i]?|jul[i]?|aug|sep|okt|nov|dec)\.?/i,wide:/^(januari|februari|mars|april|maj|juni|juli|augusti|september|oktober|november|december)/i},o={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^maj/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},r={narrow:/^[smtofl]/i,short:/^(sö|må|ti|on|to|fr|lö)/i,abbreviated:/^(sön|mån|tis|ons|tors|fre|lör)/i,wide:/^(söndag|måndag|tisdag|onsdag|torsdag|fredag|lördag)/i},a={any:[/^s/i,/^m/i,/^ti/i,/^o/i,/^to/i,/^f/i,/^l/i]},e={any:/^([fe]\.?\s?m\.?|midn(att)?|midd(ag)?|(på) (morgonen|eftermiddagen|kvällen|natten))/i},t={any:{am:/^f/i,pm:/^e/i,midnight:/^midn/i,noon:/^midd/i,morning:/morgon/i,afternoon:/eftermiddag/i,evening:/kväll/i,night:/natt/i}},CC={ordinalNumber:d({matchPattern:g,parsePattern:p,valueCallback:function C(G){return parseInt(G,10)}}),era:O({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:l,defaultParseWidth:"any"}),quarter:O({matchPatterns:i,defaultMatchWidth:"wide",parsePatterns:n,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:O({matchPatterns:s,defaultMatchWidth:"wide",parsePatterns:o,defaultParseWidth:"any"}),day:O({matchPatterns:r,defaultMatchWidth:"wide",parsePatterns:a,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:e,defaultMatchWidth:"any",parsePatterns:t,defaultParseWidth:"any"})},GC={code:"sv",formatDistance:M,formatLong:j,formatRelative:_,localize:m,match:CC,options:{weekStartsOn:1,firstWeekContainsDate:4}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},($=window.dateFns)===null||$===void 0?void 0:$.locale),{},{sv:GC})})})();

//# debugId=C765023CCF96B37D64756E2164756E21
